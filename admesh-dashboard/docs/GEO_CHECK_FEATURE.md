# GEO Check Feature Documentation

## Overview

The GEO Check tool is a free feature for brands on the AdMesh platform that analyzes and provides recommendations for optimizing content for AI-powered search engines and generative AI platforms (ChatGPT, Claude, Perplexity, etc.).

## What is GEO (Generative Engine Optimization)?

GEO represents a paradigm shift from traditional SEO to optimization for AI-powered search:

### Traditional SEO vs GEO

| Aspect | SEO | GEO |
|--------|-----|-----|
| **Focus** | Ranking high in search results | Being cited in AI responses |
| **Methods** | Keywords, backlinks, page optimization | Factual claims, structured content, authority |
| **Goal** | Click-through rates | Reference rates in AI outputs |
| **Content Style** | Keyword-optimized | Fact-dense and well-structured |

## Features

### 1. Website Content Analysis
- **Structure Score**: Analyzes heading hierarchy, lists, and organization
- **Factual Claims Score**: Evaluates evidence-based content and supporting data
- **AI Readability Score**: Measures how well content is optimized for AI consumption

### 2. AI Discoverability Assessment
- Simulates queries customers might ask AI about the brand's industry
- Analyzes sentiment and context of potential mentions
- Provides sample queries where the brand might appear

### 3. Competitive Analysis
- Share of voice compared to competitors
- Competitive mention analysis
- Market positioning insights

### 4. Actionable Recommendations
- Priority-based recommendations (High, Medium, Low)
- Specific action items for improvement
- Expected impact analysis
- Category-based organization

## Technical Implementation

### Frontend Components

#### Main Page
- **Location**: `src/app/dashboard/brand/geo-check/page.tsx`
- **Features**: Tabbed interface with overview, discoverability, content analysis, and recommendations

#### Reusable Components
- **GEOScoreCard**: `src/components/geo-check/GEOScoreCard.tsx`
  - Displays scores with progress bars and badges
  - Color-coded based on performance levels
  - Trend indicators for score changes

- **GEORecommendationCard**: `src/components/geo-check/GEORecommendationCard.tsx`
  - Expandable recommendation cards
  - Priority indicators and action items
  - Resource links and completion tracking

- **GEOInfoCard**: `src/components/geo-check/GEOInfoCard.tsx`
  - Educational content about GEO
  - SEO vs GEO comparison
  - External resource links

### Backend API

#### Endpoint
- **Route**: `/brands/geo-check`
- **Method**: POST
- **Authentication**: Required (Firebase JWT)

#### Analysis Engine
- **Website Content Analysis**: Scrapes and analyzes website structure, factual claims, and AI readability
- **AI Discoverability Simulation**: Simulates how the brand might appear in AI responses
- **Recommendation Generation**: Creates actionable recommendations based on analysis

#### Dependencies
- `aiohttp`: For asynchronous HTTP requests
- `beautifulsoup4`: For HTML parsing and content analysis
- `fastapi`: Web framework
- `pydantic`: Data validation

## User Experience

### Navigation
1. **Sidebar**: GEO Check appears in the brand sidebar with a "BETA" badge
2. **Dashboard**: Quick action button with "FREE" badge on the main brand dashboard
3. **Direct Access**: `/dashboard/brand/geo-check`

### Analysis Flow
1. **Initial State**: Educational content about GEO with call-to-action
2. **Analysis Trigger**: User clicks "Run GEO Analysis" button
3. **Loading State**: Shows progress with spinning icon
4. **Results Display**: Tabbed interface with comprehensive analysis
5. **Recommendations**: Actionable insights with priority levels

### Scoring System
- **0-39**: Needs Improvement (Red)
- **40-69**: Fair/Good (Yellow)
- **70-100**: Excellent (Green)

## Benefits for Brands

### Immediate Value
- **Free Tool**: No cost to use, adding value to the AdMesh platform
- **Educational**: Introduces brands to the concept of GEO
- **Actionable**: Provides specific recommendations for improvement

### Strategic Advantages
- **Early Adopter Advantage**: Get ahead of the curve as AI search grows
- **Higher Trust**: AI-mediated recommendations carry more weight with users
- **Future-Proof Strategy**: Prepare for the AI-driven search landscape
- **Better Content Quality**: Focus on factual, authoritative content

## Implementation Status

### Completed
- ✅ Frontend UI components and pages
- ✅ Backend API endpoint and analysis engine
- ✅ Integration with brand dashboard
- ✅ Sidebar navigation
- ✅ Educational content and resources

### Future Enhancements
- [ ] Historical analysis tracking
- [ ] Competitor comparison features
- [ ] Integration with actual AI APIs for real-time testing
- [ ] Automated monitoring and alerts
- [ ] Export functionality for reports

## Usage Analytics

The GEO Check tool can be tracked for:
- Usage frequency by brands
- Most common recommendation types
- Score improvements over time
- Feature adoption rates

## References

- [Andreessen Horowitz: GEO over SEO](https://a16z.com/geo-over-seo/)
- [Solresol: SEO is dying. What's next?](https://solresol.substack.com/p/seo-is-dying-whats-next)

## Support

For questions or issues with the GEO Check feature:
1. Check the educational content within the tool
2. Review the external resources linked in the info card
3. Contact AdMesh support through the dashboard

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Lightbulb, 
  TrendingUp, 
  ExternalLink, 
  CheckCircle,
  AlertCircle,
  Clock
} from "lucide-react";
import { useState } from "react";

interface GEORecommendation {
  priority: "high" | "medium" | "low";
  category: string;
  title: string;
  description: string;
  impact: string;
  actionItems?: string[];
  resources?: { title: string; url: string }[];
}

interface GEORecommendationCardProps {
  recommendation: GEORecommendation;
  onMarkComplete?: (recommendation: GEORecommendation) => void;
  isCompleted?: boolean;
}

export function GEORecommendationCard({
  recommendation,
  onMarkComplete,
  isCompleted = false
}: GEORecommendationCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "destructive";
      case "medium": return "secondary";
      case "low": return "outline";
      default: return "outline";
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "high": return <AlertCircle className="h-4 w-4" />;
      case "medium": return <Clock className="h-4 w-4" />;
      case "low": return <Lightbulb className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  return (
    <Card className={`transition-all ${isCompleted ? 'opacity-60 border-green-200' : ''}`}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            {isCompleted ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              getPriorityIcon(recommendation.priority)
            )}
            <div>
              <CardTitle className="text-base">{recommendation.title}</CardTitle>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline" className="text-xs">
                  {recommendation.category}
                </Badge>
                <Badge variant={getPriorityColor(recommendation.priority)} className="text-xs">
                  {recommendation.priority} priority
                </Badge>
              </div>
            </div>
          </div>
          {onMarkComplete && !isCompleted && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onMarkComplete(recommendation)}
              className="text-xs"
            >
              Mark Complete
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <p className="text-sm text-muted-foreground">{recommendation.description}</p>
        
        <div className="flex items-center gap-2 text-xs">
          <TrendingUp className="h-3 w-3 text-green-600" />
          <span className="font-medium">Expected Impact:</span>
          <span>{recommendation.impact}</span>
        </div>

        {(recommendation.actionItems || recommendation.resources) && (
          <div className="pt-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-xs p-0 h-auto"
            >
              {isExpanded ? "Hide Details" : "Show Details"}
            </Button>
          </div>
        )}

        {isExpanded && (
          <div className="space-y-3 pt-2 border-t">
            {recommendation.actionItems && (
              <div>
                <h4 className="text-sm font-medium mb-2">Action Items:</h4>
                <ul className="space-y-1">
                  {recommendation.actionItems.map((item, index) => (
                    <li key={index} className="text-xs text-muted-foreground flex items-start gap-2">
                      <span className="text-primary">•</span>
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {recommendation.resources && (
              <div>
                <h4 className="text-sm font-medium mb-2">Helpful Resources:</h4>
                <div className="space-y-1">
                  {recommendation.resources.map((resource, index) => (
                    <a
                      key={index}
                      href={resource.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-600 hover:text-blue-800 flex items-center gap-1"
                    >
                      <ExternalLink className="h-3 w-3" />
                      {resource.title}
                    </a>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

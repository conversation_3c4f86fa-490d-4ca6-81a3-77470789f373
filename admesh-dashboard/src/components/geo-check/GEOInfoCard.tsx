import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Brain, 
  ExternalLink, 
  ArrowRight,
  Sparkles,
  Target,
  TrendingUp
} from "lucide-react";

export function GEOInfoCard() {
  const geoVsSeoComparison = [
    {
      aspect: "Focus",
      seo: "Ranking high in search results",
      geo: "Being cited in AI responses"
    },
    {
      aspect: "Methods",
      seo: "Keywords, backlinks, page optimization",
      geo: "Factual claims, structured content, authority"
    },
    {
      aspect: "Goal",
      seo: "Click-through rates",
      geo: "Reference rates in AI outputs"
    },
    {
      aspect: "Content Style",
      seo: "Keyword-optimized",
      geo: "Fact-dense and well-structured"
    }
  ];

  const benefits = [
    {
      icon: <Sparkles className="h-4 w-4" />,
      title: "Early Adopter Advantage",
      description: "Get ahead of the curve as AI search grows"
    },
    {
      icon: <Target className="h-4 w-4" />,
      title: "Higher Trust",
      description: "AI-mediated recommendations carry more weight"
    },
    {
      icon: <TrendingUp className="h-4 w-4" />,
      title: "Future-Proof Strategy",
      description: "Prepare for the AI-driven search landscape"
    },
    {
      icon: <Brain className="h-4 w-4" />,
      title: "Better Content Quality",
      description: "Focus on factual, authoritative content"
    }
  ];

  return (
    <Card className="border-blue-200 bg-blue-50/50 dark:bg-blue-950/20 dark:border-blue-800">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
          <Brain className="h-5 w-5" />
          What is Generative Engine Optimization (GEO)?
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="text-sm text-blue-600 dark:text-blue-400">
          <p className="mb-4">
            GEO is the new frontier of digital marketing, focusing on optimizing your content for AI-powered search engines 
            like ChatGPT, Claude, and Perplexity rather than traditional search engines.
          </p>
          
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-3 text-blue-700 dark:text-blue-300">SEO vs GEO Comparison:</h4>
              <div className="space-y-2">
                {geoVsSeoComparison.map((item, index) => (
                  <div key={index} className="grid grid-cols-3 gap-2 text-xs">
                    <div className="font-medium">{item.aspect}:</div>
                    <div className="text-gray-600 dark:text-gray-400">
                      <Badge variant="outline" className="text-xs">SEO</Badge> {item.seo}
                    </div>
                    <div className="text-blue-600 dark:text-blue-400">
                      <Badge variant="default" className="text-xs">GEO</Badge> {item.geo}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-3 text-blue-700 dark:text-blue-300">Why GEO Matters:</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <div className="text-blue-600 dark:text-blue-400 mt-0.5">
                      {benefit.icon}
                    </div>
                    <div>
                      <div className="font-medium text-xs">{benefit.title}</div>
                      <div className="text-xs text-gray-600 dark:text-gray-400">{benefit.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="pt-2 border-t border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-xs text-blue-700 dark:text-blue-300">Learn More About GEO</h4>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Dive deeper into the shift from SEO to GEO</p>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={() => window.open("https://a16z.com/geo-over-seo/", "_blank")}
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    a16z Article
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={() => window.open("https://solresol.substack.com/p/seo-is-dying-whats-next", "_blank")}
                  >
                    <ExternalLink className="h-3 w-3 mr-1" />
                    Industry Analysis
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
